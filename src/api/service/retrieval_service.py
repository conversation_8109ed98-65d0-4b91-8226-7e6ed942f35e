"""
检索服务层 - 业务逻辑层

📤 职责：
- 处理检索查询业务逻辑
- 管理流式响应
- 处理历史记录管理
- 参数转换和验证

三层架构：Route → Service → RAG
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional, AsyncGenerator

from src.api.schemas.request.retrieval import (
    RetrievalQueryRequest,
    RetrievalStreamRequest,
    RetrievalHistoryRequest,
    RetrievalHistoryQueryRequest
)
from src.api.schemas.response.retrieval import (
    RetrievalQueryResponse,
    RetrievalStreamChunk,
    RetrievalHistoryResponse,
    RetrievalHistorySaveResponse,
    RetrievalHistoryClearResponse,
    RetrievalStatsResponse,
    RetrievalHistoryItem
)
from src.rag.infrastructure.schemas.base import QueryParam
from src.rag.service.retrieval import knowledge_graph_retriever
from src.rag.infrastructure import redis_client
from src.rag.llm import call_llm_stream
from src.rag.tools import logger


class RetrievalService:
    """检索服务"""

    def __init__(self):
        self.history_key_prefix = "retrieval_history"
        self.stats_key = "retrieval_stats"

    def _convert_request_to_query_param(self, request: RetrievalQueryRequest) -> QueryParam:
        """将请求参数转换为RAG层的QueryParam"""
        # 映射响应格式
        response_type_mapping = {
            "单段落": "Single Paragraph",
            "多段落": "Multiple Paragraphs", 
            "要点": "List of Points"
        }
        
        # 映射检索模式
        mode_mapping = {
            "naive": "local",  # 前端的naive对应后端的local
            "global": "global",
            "mix": "mix"
        }

        return QueryParam(
            mode=mode_mapping.get(request.mode, "mix"),
            only_need_context=request.context_only,
            only_need_prompt=request.prompt_only,
            response_type=response_type_mapping.get(request.response_format, "Multiple Paragraphs"),
            stream=request.stream,
            top_k=request.top_k,
            max_token_for_text_unit=request.text_unit_max_tokens,
            max_token_for_global_context=request.global_context_max_tokens,
            max_token_for_local_context=request.text_unit_max_tokens,
        )

    async def query_knowledge(self, request: RetrievalQueryRequest) -> RetrievalQueryResponse:
        """
        知识检索查询

        Args:
            request: 检索查询请求

        Returns:
            检索查询响应
        """
        start_time = time.time()

        try:
            # 转换参数
            query_param = self._convert_request_to_query_param(request)

            # 根据不同模式处理
            if request.context_only:
                # 仅需上下文模式：只返回检索到的上下文，不调用LLM
                response = await self._get_context_only(request.query, query_param)
            elif request.prompt_only:
                # 仅需提示模式：返回拼接好的prompt，不调用LLM
                response = await self._get_prompt_only(request.query, query_param)
            else:
                # 正常模式：检索上下文 + LLM生成回答
                response = await knowledge_graph_retriever.query(
                    query=request.query,
                    param=query_param
                )

            processing_time = time.time() - start_time

            # 记录统计信息
            await self._update_stats(request.mode, processing_time)

            return RetrievalQueryResponse(
                success=True,
                response=response,
                mode=request.mode,
                processing_time=processing_time,
                message="查询成功"
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"检索查询失败: {e}")

            return RetrievalQueryResponse(
                success=False,
                response=f"查询失败: {str(e)}",
                mode=request.mode,
                processing_time=processing_time,
                message=f"查询失败: {str(e)}"
            )

    async def query_knowledge_stream(
        self,
        request: RetrievalStreamRequest
    ) -> AsyncGenerator[RetrievalStreamChunk, None]:
        """
        流式知识检索查询

        Args:
            request: 流式检索查询请求

        Yields:
            流式响应数据块
        """
        start_time = time.time()

        try:
            # 转换参数
            query_param = self._convert_request_to_query_param(request)

            # 根据不同模式处理
            if request.context_only:
                # 仅需上下文模式：只返回检索到的上下文
                context = await self._get_context_only(request.query, query_param)
                yield RetrievalStreamChunk(content=context, finished=True)

            elif request.prompt_only:
                # 仅需提示模式：返回拼接好的prompt
                prompt = await self._get_prompt_only(request.query, query_param)
                yield RetrievalStreamChunk(content=prompt, finished=True)

            else:
                # 流式模式：检索上下文 + 流式LLM生成
                async for chunk in self._stream_llm_response(request.query, query_param):
                    yield chunk

            processing_time = time.time() - start_time
            await self._update_stats(request.mode, processing_time)

        except Exception as e:
            logger.error(f"流式检索查询失败: {e}")
            yield RetrievalStreamChunk(
                error=f"查询失败: {str(e)}",
                finished=True
            )

    def _split_response_to_chunks(self, response: str, chunk_size: int = 50) -> List[str]:
        """将响应分割为流式数据块"""
        words = response.split()
        chunks = []
        
        for i in range(0, len(words), chunk_size):
            chunk_words = words[i:i + chunk_size]
            chunk = " ".join(chunk_words)
            if i + chunk_size < len(words):
                chunk += " "
            chunks.append(chunk)
            
        return chunks if chunks else [""]

    async def _get_context_only(self, query: str, query_param: QueryParam) -> str:
        """
        仅需上下文模式：只返回检索到的上下文，不调用LLM

        Args:
            query: 查询内容
            query_param: 查询参数

        Returns:
            检索到的上下文
        """
        try:
            # 设置为仅需上下文模式
            query_param.only_need_context = True

            # 调用检索服务获取上下文
            context = await knowledge_graph_retriever.query(
                query=query,
                param=query_param
            )

            return context

        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            return f"获取上下文失败: {str(e)}"

    async def _get_prompt_only(self, query: str, query_param: QueryParam) -> str:
        """
        仅需提示模式：返回拼接好的prompt，不调用LLM

        Args:
            query: 查询内容
            query_param: 查询参数

        Returns:
            拼接好的prompt
        """
        try:
            # 设置为仅需提示模式
            query_param.only_need_prompt = True

            # 调用检索服务获取拼接好的prompt
            prompt = await knowledge_graph_retriever.query(
                query=query,
                param=query_param
            )

            return prompt

        except Exception as e:
            logger.error(f"获取提示失败: {e}")
            return f"获取提示失败: {str(e)}"

    async def _stream_llm_response(
        self,
        query: str,
        query_param: QueryParam
    ) -> AsyncGenerator[RetrievalStreamChunk, None]:
        """
        流式LLM响应生成

        Args:
            query: 查询内容
            query_param: 查询参数

        Yields:
            流式响应数据块
        """
        try:
            # 首先获取上下文
            query_param.only_need_context = True
            context = await knowledge_graph_retriever.query(
                query=query,
                param=query_param
            )

            # 构建提示词
            from src.rag.llm import PROMPTS
            prompt_template = PROMPTS.get("rag_response", PROMPTS.get("naive_rag_response", ""))

            if not prompt_template:
                yield RetrievalStreamChunk(content=context, finished=True)
                return

            prompt = prompt_template.format(
                context_data=context,
                response_type=query_param.response_type
            )

            # 流式调用LLM
            async for chunk in call_llm_stream(prompt):
                yield RetrievalStreamChunk(content=chunk, finished=False)

            # 发送完成信号
            yield RetrievalStreamChunk(content="", finished=True)

        except Exception as e:
            logger.error(f"流式LLM响应失败: {e}")
            yield RetrievalStreamChunk(
                error=f"流式响应失败: {str(e)}",
                finished=True
            )

    async def save_history(self, request: RetrievalHistoryRequest) -> RetrievalHistorySaveResponse:
        """
        保存检索历史

        Args:
            request: 历史保存请求

        Returns:
            历史保存响应
        """
        try:
            if not redis_client:
                return RetrievalHistorySaveResponse(
                    success=False,
                    saved_count=0,
                    message="Redis客户端未初始化"
                )

            # 生成会话ID
            session_id = str(uuid.uuid4())
            history_key = f"{self.history_key_prefix}:{session_id}"

            # 保存历史记录
            history_data = {
                "session_id": session_id,
                "timestamp": datetime.now().isoformat(),
                "messages": request.history
            }

            await redis_client.redis.set(
                history_key,
                json.dumps(history_data, ensure_ascii=False),
                ex=7 * 24 * 3600  # 7天过期
            )

            return RetrievalHistorySaveResponse(
                success=True,
                saved_count=len(request.history),
                message="历史记录保存成功"
            )

        except Exception as e:
            logger.error(f"保存历史记录失败: {e}")
            return RetrievalHistorySaveResponse(
                success=False,
                saved_count=0,
                message=f"保存失败: {str(e)}"
            )

    async def get_history(self, request: RetrievalHistoryQueryRequest) -> RetrievalHistoryResponse:
        """
        获取检索历史

        Args:
            request: 历史查询请求

        Returns:
            历史查询响应
        """
        try:
            if not redis_client:
                return RetrievalHistoryResponse(
                    success=False,
                    data=[],
                    total=0,
                    page=request.page,
                    page_size=request.page_size,
                    message="Redis客户端未初始化"
                )

            # 获取所有历史记录键
            pattern = f"{self.history_key_prefix}:*"
            keys = []
            async for key in redis_client.redis.scan_iter(match=pattern):
                keys.append(key)

            if not keys:
                return RetrievalHistoryResponse(
                    success=True,
                    data=[],
                    total=0,
                    page=request.page,
                    page_size=request.page_size,
                    message="暂无历史记录"
                )

            # 获取历史记录数据
            history_items = []
            for key in keys:
                data = await redis_client.redis.get(key)
                if data:
                    history_data = json.loads(data)
                    for msg in history_data.get("messages", []):
                        history_items.append(RetrievalHistoryItem(
                            id=msg.get("id", str(uuid.uuid4())),
                            role=msg.get("role", "user"),
                            content=msg.get("content", ""),
                            timestamp=datetime.fromisoformat(history_data["timestamp"]),
                            mode=msg.get("mode"),
                            processing_time=msg.get("processing_time"),
                            is_error=msg.get("isError", False),
                            mermaid_rendered=msg.get("mermaidRendered", False)
                        ))

            # 排序和分页
            history_items.sort(key=lambda x: x.timestamp, reverse=True)
            total = len(history_items)
            start_idx = (request.page - 1) * request.page_size
            end_idx = start_idx + request.page_size
            paginated_items = history_items[start_idx:end_idx]

            return RetrievalHistoryResponse(
                success=True,
                data=paginated_items,
                total=total,
                page=request.page,
                page_size=request.page_size,
                message="获取历史记录成功"
            )

        except Exception as e:
            logger.error(f"获取历史记录失败: {e}")
            return RetrievalHistoryResponse(
                success=False,
                data=[],
                total=0,
                page=request.page,
                page_size=request.page_size,
                message=f"获取失败: {str(e)}"
            )

    async def clear_history(self) -> RetrievalHistoryClearResponse:
        """
        清除检索历史

        Returns:
            历史清除响应
        """
        try:
            if not redis_client:
                return RetrievalHistoryClearResponse(
                    success=False,
                    cleared_count=0,
                    message="Redis客户端未初始化"
                )

            # 获取所有历史记录键
            pattern = f"{self.history_key_prefix}:*"
            keys = []
            async for key in redis_client.redis.scan_iter(match=pattern):
                keys.append(key)

            if keys:
                # 删除所有历史记录
                await redis_client.redis.delete(*keys)
                cleared_count = len(keys)
            else:
                cleared_count = 0

            return RetrievalHistoryClearResponse(
                success=True,
                cleared_count=cleared_count,
                message="历史记录清除成功"
            )

        except Exception as e:
            logger.error(f"清除历史记录失败: {e}")
            return RetrievalHistoryClearResponse(
                success=False,
                cleared_count=0,
                message=f"清除失败: {str(e)}"
            )

    async def _update_stats(self, mode: str, processing_time: float):
        """更新统计信息"""
        try:
            if not redis_client:
                return

            stats_data = await redis_client.redis.get(self.stats_key)
            if stats_data:
                stats = json.loads(stats_data)
            else:
                stats = {
                    "total_queries": 0,
                    "total_sessions": 0,
                    "total_processing_time": 0.0,
                    "mode_distribution": {},
                    "recent_activity": []
                }

            # 更新统计
            stats["total_queries"] += 1
            stats["total_processing_time"] += processing_time
            stats["mode_distribution"][mode] = stats["mode_distribution"].get(mode, 0) + 1

            # 添加最近活动
            activity = {
                "timestamp": datetime.now().isoformat(),
                "mode": mode,
                "processing_time": processing_time
            }
            stats["recent_activity"].insert(0, activity)
            stats["recent_activity"] = stats["recent_activity"][:100]  # 保留最近100条

            await redis_client.redis.set(self.stats_key, json.dumps(stats, ensure_ascii=False))

        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")


# 创建全局服务实例
retrieval_service = RetrievalService()
