"""
查询服务

API 层的查询业务逻辑，作为 RAG 模块的封装
"""

import time
from typing import Dict, Any, Optional, List

from src.api.schemas.request.graphs import GraphSearchRequest as GraphQueryRequest
from src.api.schemas.request.requests import QueryRequest
from src.api.schemas.response.graphs import GraphSearchResponse as GraphQueryResponse
from src.api.schemas.response.responses import QueryResponse
from src.api.service.graph_service import graph_service
from src.rag.service.retrieval import knowledge_graph_retriever


class QueryService:
    """查询服务"""

    async def query_knowledge(self, request: QueryRequest) -> QueryResponse:
        """
        知识查询
        
        Args:
            request: 查询请求
            
        Returns:
            查询响应
        """
        start_time = time.time()

        # 调用 RAG 检索服务
        results = await knowledge_graph_retriever.mix_retrieval(
            query=request.query,
            top_k=request.top_k
        )

        processing_time = time.time() - start_time

        return QueryResponse(
            query=request.query,
            results=results["items"],
            total_count=results["total_count"],
            processing_time=processing_time,
            message="查询成功"
        )
            
    async def query_graph(self, request: GraphQueryRequest) -> GraphQueryResponse:
        """
        图查询
        
        Args:
            request: 图查询请求
            
        Returns:
            图查询响应
        """
        # 调用图服务执行查询
        result = await graph_service.execute_query(
            query=request.query,
            limit=request.limit
        )

        return GraphQueryResponse(
            query=request.query,
            nodes=result["nodes"],
            edges=result["edges"],
            total_nodes=len(result["nodes"]),
            total_edges=len(result["edges"]),
            message="图查询成功"
        )
            

    async def get_entity_neighbors(
        self,
        entity_id: str,
        max_depth: int = 2
    ) -> Dict[str, Any]:
        """
        获取实体邻居

        Args:
            entity_id: 实体ID
            max_depth: 最大深度

        Returns:
            邻居信息
        """
        # 调用图服务获取邻居
        result = await graph_service.get_entity_neighbors(
            entity_id=entity_id,
            max_depth=max_depth
        )

        return {
            "entity_id": entity_id,
            "neighbors": result["neighbors"],
            "relationships": result["relationships"],
            "depth": max_depth
        }

    async def search_entities(
        self,
        entity_name: Optional[str] = None,
        entity_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        搜索实体

        Args:
            entity_name: 实体名称
            entity_type: 实体类型
            limit: 结果限制

        Returns:
            实体列表
        """
        # 调用图服务搜索实体
        entities = await graph_service.search_entities(
            entity_name=entity_name,
            entity_type=entity_type,
            limit=limit
        )

        return entities

    async def search_relationships(
        self,
        source_entity: Optional[str] = None,
        target_entity: Optional[str] = None,
        relation_type: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        搜索关系

        Args:
            source_entity: 源实体
            target_entity: 目标实体
            relation_type: 关系类型
            limit: 结果限制

        Returns:
            关系列表
        """
        # 调用图服务搜索关系
        relationships = await graph_service.search_relationships(
            source_entity=source_entity,
            target_entity=target_entity,
            relation_type=relation_type,
            limit=limit
        )

        return relationships


# 全局查询服务实例
query_service = QueryService()
